#!/usr/bin/env python3
"""
调试无限滚动加载问题的脚本
"""

import sys
import os
from datetime import datetime, timezone, timedelta

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src-python', 'python'))

from photo_dedup.collector import collect_photos_with_smart_cache_by_date_range
from photo_dedup.database import initialize_database, get_cached_date_range, is_date_range_cached

def debug_date_range_queries():
    """调试日期范围查询"""
    
    # 替换为您的实际Photos库路径
    library_path = "/Users/<USER>/Pictures/Photos Library.photoslibrary"
    
    print("🔍 调试无限滚动加载问题")
    print(f"📂 Photos库路径: {library_path}")
    print("=" * 80)
    
    # 模拟前端的查询序列
    current_date = datetime.now(tz=timezone.utc)
    days_back = 5  # 每次查询5天
    max_photos_per_batch = 100  # 每批最多100张
    
    print(f"📅 当前时间: {current_date}")
    print(f"🔢 每次查询天数: {days_back}")
    print(f"📸 每批最大照片数: {max_photos_per_batch}")
    print("=" * 80)
    
    # 初始化数据库以检查缓存状态
    Session = initialize_database()
    session = Session()
    
    try:
        # 检查当前缓存状态
        cache = get_cached_date_range(session, library_path)
        if cache:
            print(f"📚 当前缓存范围: {cache.start_date} 到 {cache.end_date}")
            print(f"📊 缓存照片数量: {cache.photo_count}")
        else:
            print("📚 当前没有缓存数据")
        print("=" * 80)
        
        # 模拟连续的查询
        for i in range(3):  # 模拟3次查询
            print(f"\n🔍 第 {i+1} 次查询:")
            
            # 计算查询的日期范围
            search_end = current_date - timedelta(days=i * days_back)
            search_start = search_end - timedelta(days=days_back)
            
            start_timestamp = int(search_start.timestamp())
            end_timestamp = int(search_end.timestamp())
            
            print(f"📅 查询日期范围: {search_start} 到 {search_end}")
            print(f"🔢 时间戳范围: {start_timestamp} 到 {end_timestamp}")
            
            # 检查这个范围是否已缓存
            is_cached = is_date_range_cached(session, library_path, search_start, search_end)
            print(f"🔍 缓存状态: {'已缓存' if is_cached else '未缓存'}")
            
            # 执行查询
            try:
                photos = collect_photos_with_smart_cache_by_date_range(
                    library_path=library_path,
                    start_date=start_timestamp,
                    end_date=end_timestamp,
                    max_photos=max_photos_per_batch
                )
                
                print(f"📸 查询结果: {len(photos)} 张照片")
                
                if photos:
                    # 显示前几张照片的信息
                    print("📋 前3张照片信息:")
                    for j, photo in enumerate(photos[:3]):
                        if isinstance(photo, dict):
                            filename = photo.get('filename', 'Unknown')
                            date_taken = photo.get('date_taken', 'Unknown')
                        else:
                            filename = photo.filename
                            date_taken = photo.date_taken
                        print(f"  {j+1}. {filename} - {date_taken}")
                
            except Exception as e:
                print(f"❌ 查询失败: {e}")
            
            print("-" * 40)
            
    finally:
        session.close()

def analyze_photo_distribution():
    """分析照片在时间上的分布"""
    
    library_path = "/Users/<USER>/Pictures/Photos Library.photoslibrary"
    
    print("\n📊 分析照片时间分布")
    print("=" * 80)
    
    # 查询最近30天的照片分布
    current_date = datetime.now(tz=timezone.utc)
    
    for days_ago in [0, 5, 10, 15, 20, 25, 30]:
        search_end = current_date - timedelta(days=days_ago)
        search_start = search_end - timedelta(days=5)  # 每次查询5天
        
        start_timestamp = int(search_start.timestamp())
        end_timestamp = int(search_end.timestamp())
        
        try:
            photos = collect_photos_with_smart_cache_by_date_range(
                library_path=library_path,
                start_date=start_timestamp,
                end_date=end_timestamp,
                max_photos=0  # 不限制数量，看实际有多少
            )
            
            print(f"📅 {search_start.strftime('%Y-%m-%d')} 到 {search_end.strftime('%Y-%m-%d')}: {len(photos)} 张照片")
            
        except Exception as e:
            print(f"❌ 查询 {search_start.strftime('%Y-%m-%d')} 到 {search_end.strftime('%Y-%m-%d')} 失败: {e}")

if __name__ == "__main__":
    print("🚀 开始调试无限滚动加载问题")
    
    # 检查Photos库路径是否存在
    library_path = "/Users/<USER>/Pictures/Photos Library.photoslibrary"
    if not os.path.exists(library_path):
        print(f"❌ Photos库路径不存在: {library_path}")
        print("请修改脚本中的library_path变量为您的实际Photos库路径")
        sys.exit(1)
    
    try:
        debug_date_range_queries()
        analyze_photo_distribution()
        
        print("\n✅ 调试完成")
        print("\n💡 如果发现问题:")
        print("1. 检查是否有某个5天范围内确实有700+张照片")
        print("2. 检查缓存逻辑是否正确识别已缓存的范围")
        print("3. 检查前端是否正确传递maxPhotos参数")
        
    except Exception as e:
        print(f"❌ 调试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
