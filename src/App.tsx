import { useState, useEffect } from 'react';
import { invoke, convertFileSrc } from '@tauri-apps/api/core';
// import { open } from '@tauri-apps/plugin-dialog';
// import { readDir } from '@tauri-apps/plugin-fs';
import './App.css';

interface Photo {
    uuid: string;
    filename: string;
    original_path: string;
    date_taken: number;
    width: number;
    height: number;
    file_size: number;
    thumbnail_path?: string;
    // 算法计算结果
    phash?: string;
    ahash?: string;
    dhash?: string;
    whash?: string;
    similarity_scores?: { [key: string]: number }; // 与其他照片的相似度分数
}

interface SimilarityGroup {
    group_id: string;
    photos: Photo[];
    similarity_score: number;
    photo_count: number;
}

function App() {
    const [photos, setPhotos] = useState<Photo[]>([]);
    const [similarityGroups, setSimilarityGroups] = useState<SimilarityGroup[]>(
        []
    );
    const [selectedPath, setSelectedPath] = useState<string>('');
    const [isLoading, setIsLoading] = useState(false);
    const [activeTab, setActiveTab] = useState<'photos' | 'duplicates'>(
        'photos'
    );
    const [defaultLibraryChecked, setDefaultLibraryChecked] = useState(false);
    const [photosPerRow, setPhotosPerRow] = useState<number>(8); // 默认每行8张图片
    const [duplicatesPerRow, setDuplicatesPerRow] = useState<number>(6); // 相似照片默认每行6张
    const [daysBack, setDaysBack] = useState<number>(5); // 默认查看最近5天的照片

    // 算法参数状态
    const [algorithmConfig, setAlgorithmConfig] = useState({
        timeThresholdSeconds: 300, // 5分钟
        similarityThreshold: 0.85, // 85%
        showAlgorithmDetails: false // 是否显示算法详情面板
    });

    // 分批加载相关状态
    const [batchLoadingEnabled, setBatchLoadingEnabled] = useState(true); // 默认启用分批加载
    const [minPhotosRequired, setMinPhotosRequired] = useState(50); // 最少需要的照片数量
    const [currentDate, setCurrentDate] = useState<Date>(() => {
        // 确保不会搜索未来日期，最多到今天
        const today = new Date();
        today.setHours(23, 59, 59, 999); // 设置为今天的最后一刻
        return today;
    }); // 当前加载到的日期
    const [photosByDate, setPhotosByDate] = useState<Map<string, Photo[]>>(
        new Map()
    ); // 按日期分组的照片
    const [isLoadingMore, setIsLoadingMore] = useState(false);
    const [hasMorePhotos, setHasMorePhotos] = useState(true);
    const [consecutiveEmptyDays, setConsecutiveEmptyDays] = useState(0); // 连续空日期计数

    // 优化分析相关状态
    const [useOptimizedAnalysis, setUseOptimizedAnalysis] = useState(true); // 默认启用优化分析
    const [optimizedConfig, setOptimizedConfig] = useState({
        batchSize: 50,
        maxWorkers: 4,
        ahashThreshold: 0.75, // 降低阈值以找到更多相似照片
        phashThreshold: 0.70  // 降低阈值以找到更多相似照片
    });

    // 控制每张照片的算法详情显示
    const [expandedPhotoDetails, setExpandedPhotoDetails] = useState<
        Set<string>
    >(new Set());

    const [progress, setProgress] = useState<{
        phase: string;
        current: number;
        total: number;
        message: string;
    } | null>(null);

    // 按日期分组照片
    const groupPhotosByDate = (photosToGroup: Photo[]) => {
        const grouped = new Map<string, Photo[]>();
        photosToGroup.forEach(photo => {
            const date = new Date(photo.date_taken * 1000);
            const dateKey = date.toDateString();
            if (!grouped.has(dateKey)) {
                grouped.set(dateKey, []);
            }
            grouped.get(dateKey)!.push(photo);
        });

        // 按日期排序每组内的照片
        grouped.forEach((photos, dateKey) => {
            photos.sort((a, b) => b.date_taken - a.date_taken);
        });

        setPhotosByDate(grouped);
        console.log(`📅 照片按日期分组完成，共 ${grouped.size} 个日期组`);
    };

    // 切换照片详情显示
    const togglePhotoDetails = (photoUuid: string) => {
        setExpandedPhotoDetails(prev => {
            const newSet = new Set(prev);
            if (newSet.has(photoUuid)) {
                newSet.delete(photoUuid);
            } else {
                newSet.add(photoUuid);
            }
            return newSet;
        });
    };

    // 在组件加载时自动检测默认 Photos Library
    useEffect(() => {
        // 防止React StrictMode导致的重复调用
        let isCancelled = false;

        const initializeLibrary = async () => {
            if (!isCancelled) {
                await checkDefaultPhotosLibrary();
            }
        };

        initializeLibrary();

        return () => {
            isCancelled = true;
        };
    }, []);

    // 监听时间范围变化，自动重新加载照片
    useEffect(() => {
        if (selectedPath && photos.length > 0) {
            console.log(`🔄 时间范围已更改为 ${daysBack} 天，重新加载照片...`);
            loadPhotos(selectedPath);
        }
    }, [daysBack]);

    // 响应式布局：根据屏幕宽度自动调整列数
    useEffect(() => {
        const handleResize = () => {
            const width = window.innerWidth;
            if (width <= 576) {
                setPhotosPerRow(Math.min(photosPerRow, 4)); // 小屏幕最多4列
                setDuplicatesPerRow(Math.min(duplicatesPerRow, 4));
            } else if (width <= 768) {
                setPhotosPerRow(Math.min(photosPerRow, 6)); // 中等屏幕最多6列
                setDuplicatesPerRow(Math.min(duplicatesPerRow, 5));
            }
            // 大屏幕保持用户设置的值
        };

        handleResize(); // 初始调用
        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, [photosPerRow, duplicatesPerRow]);

    // 改进的无限滚动监听
    useEffect(() => {
        if (!batchLoadingEnabled) return;

        const handleScroll = () => {
            if (isLoadingMore || !hasMorePhotos) {
                console.log('📜 滚动被阻止:', { isLoadingMore, hasMorePhotos });
                return;
            }

            const scrollTop =
                window.pageYOffset || document.documentElement.scrollTop;
            const windowHeight = window.innerHeight;
            const documentHeight = document.documentElement.scrollHeight;

            // 增加触发距离到500px，确保在不同屏幕尺寸下都能正常触发
            const triggerDistance = 500;
            const shouldTrigger = scrollTop + windowHeight >= documentHeight - triggerDistance;

            console.log('📜 滚动检测:', {
                scrollTop: Math.round(scrollTop),
                windowHeight,
                documentHeight,
                triggerDistance,
                shouldTrigger,
                distanceFromBottom: documentHeight - (scrollTop + windowHeight)
            });

            if (shouldTrigger) {
                console.log('📜 触发无限滚动 - 加载下一天的照片');
                loadNextDay();
            }
        };

        window.addEventListener('scroll', handleScroll);
        return () => window.removeEventListener('scroll', handleScroll);
    }, [
        batchLoadingEnabled,
        isLoadingMore,
        hasMorePhotos,
        currentDate,
        selectedPath,
        consecutiveEmptyDays
    ]);

    async function checkDefaultPhotosLibrary() {
        try {
            console.log('🏁 checkDefaultPhotosLibrary 开始');
            const defaultPath: string = await invoke('get_default_photos_library', {
                reason: 'checking default Photos Library'
            });

            if (defaultPath) {
                console.log('✅ 找到默认 Photos Library:', defaultPath);
                setSelectedPath(defaultPath);
                await loadPhotos(defaultPath);
            } else {
                console.log('⚠️ 未找到默认 Photos Library');
            }
        } catch (error) {
            console.error('❌ 检查默认 Photos Library 失败:', error);
        } finally {
            setDefaultLibraryChecked(true);
            console.log('🏁 checkDefaultPhotosLibrary 完成');
        }
    }

    async function loadPhotos(path: string) {
        if (!path) {
            console.error('❌ 路径为空，无法加载照片');
            return;
        }

        try {
            setIsLoading(true);
            setProgress({
                phase: 'loading',
                current: 0,
                total: 100,
                message: '正在加载照片...'
            });

            console.log('🚀 loadPhotos 开始，路径:', path);
            console.log('📊 当前配置:', {
                daysBack,
                batchLoadingEnabled,
                minPhotosRequired,
                useOptimizedAnalysis
            });

            if (batchLoadingEnabled) {
                // 分批加载模式
                console.log('📦 启用分批加载模式');
                setPhotos([]); // 清空现有照片
                setPhotosByDate(new Map()); // 清空按日期分组的照片
                setSimilarityGroups([]); // 清空相似组

                // 确保currentDate设置为今天的最后一刻，这样搜索会从今天开始
                const today = new Date();
                today.setHours(23, 59, 59, 999);
                setCurrentDate(today);
                console.log(`📅 设置搜索起始日期为今天: ${today.toDateString()}`);

                setHasMorePhotos(true);
                setConsecutiveEmptyDays(0); // 重置连续空日期计数

                // 立即设置selectedPath，确保后续函数可以使用
                setSelectedPath(path);

                // 智能加载：确保至少获取指定数量的照片
                console.log(`📅 使用智能加载模式，目标至少${minPhotosRequired}张照片`);
                await loadPhotosUntilMinimum(minPhotosRequired, path);
            } else {
                // 传统模式：一次性加载所有照片（不限制数量）
                console.log('📦 使用传统加载模式');

                // 立即设置selectedPath，确保后续函数可以使用
                setSelectedPath(path);

                console.log('📞 调用 collect_photos_from_library，参数:', {
                    libraryPath: path,
                    daysBack: daysBack,
                    maxPhotos: 0
                });

                const loadedPhotos: Photo[] = await invoke(
                    'collect_photos_from_library',
                    {
                        libraryPath: path, // 使用驼峰命名
                        daysBack: daysBack,
                        maxPhotos: 0 // 0表示不限制数量
                    }
                );

                console.log(
                    '📸 collect_photos_from_library 返回照片数量:',
                    loadedPhotos.length
                );
                setPhotos(loadedPhotos);

                // 分析相似性
                console.log('🔍 开始分析照片相似性...');
                await analyzePhotos(loadedPhotos);
            }
        } catch (error) {
            console.error('❌ 加载照片失败:', error);
            console.error('错误详情:', {
                message: String(error),
                type: typeof error,
                stack: error instanceof Error ? error.stack : 'No stack trace'
            });
            alert('加载照片时出错: ' + error);
        } finally {
            setIsLoading(false);
            setProgress(null);
            console.log('🏁 loadPhotos 完成');
        }
    }

    // 改进的加载下一天函数
    async function loadNextDay() {
        if (isLoadingMore || !hasMorePhotos) {
            console.log('📜 loadNextDay 被阻止:', { isLoadingMore, hasMorePhotos, consecutiveEmptyDays });
            return;
        }

        try {
            setIsLoadingMore(true);

            // 传统的单日加载
            const nextDate = new Date(currentDate);
            nextDate.setDate(nextDate.getDate() - 1);

            console.log(`📅 准备加载下一天: ${nextDate.toDateString()}`);

            const dayPhotos = await loadPhotosForDate(
                nextDate,
                undefined,
                selectedPath
            );

            if (dayPhotos.length === 0) {
                // 增加连续空日期计数
                const newConsecutiveEmptyDays = consecutiveEmptyDays + 1;
                setConsecutiveEmptyDays(newConsecutiveEmptyDays);
                console.log(`📭 ${nextDate.toDateString()} 没有照片，连续空日期: ${newConsecutiveEmptyDays}`);

                // 只有连续7天没有照片才停止加载
                if (newConsecutiveEmptyDays >= 7) {
                    console.log('🛑 连续7天没有照片，停止加载');
                    setHasMorePhotos(false);
                } else {
                    // 继续尝试加载前一天
                    setCurrentDate(nextDate);
                }
            } else {
                // 重置连续空日期计数
                setConsecutiveEmptyDays(0);
                setCurrentDate(nextDate);
                console.log(`✅ ${nextDate.toDateString()} 加载了 ${dayPhotos.length} 张照片`);
            }
        } catch (error) {
            console.error('❌ loadNextDay 失败:', error);
        } finally {
            setIsLoadingMore(false);
        }
    }

    // 智能加载：持续加载照片直到达到最小数量
    async function loadPhotosUntilMinimum(
        minPhotos: number,
        libraryPath: string
    ) {
        console.log(`🎯 开始智能加载，目标至少 ${minPhotos} 张照片`);

        let totalPhotos: Photo[] = [];
        let currentSearchDate = new Date();
        let daysSearched = 0;
        const maxDaysToSearch = 365; // 最多搜索一年
        let initialDaysBack = daysBack; // 初始搜索天数

        try {
            while (totalPhotos.length < minPhotos && daysSearched < maxDaysToSearch) {
                console.log(`🔍 第 ${Math.floor(daysSearched / initialDaysBack) + 1} 轮搜索，已获得 ${totalPhotos.length} 张照片`);

                // 每次搜索指定天数的范围
                const batchPhotos = await loadPhotosForDateRange(
                    currentSearchDate,
                    initialDaysBack,
                    undefined,
                    libraryPath
                );

                if (batchPhotos.length > 0) {
                    // 过滤重复照片（基于UUID）
                    const existingUuids = new Set(totalPhotos.map(p => p.uuid));
                    const newPhotos = batchPhotos.filter(p => !existingUuids.has(p.uuid));

                    totalPhotos.push(...newPhotos);
                    console.log(`📸 本轮新增 ${newPhotos.length} 张照片，总计 ${totalPhotos.length} 张`);

                    // 如果这是第一批照片，更新currentDate为搜索的起始日期（而不是照片的日期）
                    // 这样可以确保UI显示从今天开始，而不是跳到照片的实际日期
                    if (totalPhotos.length === newPhotos.length) {
                        // 第一批照片，保持currentDate为搜索起始日期
                        setCurrentDate(new Date(currentSearchDate));
                        console.log(`📅 设置显示起始日期为: ${currentSearchDate.toDateString()}`);
                    }
                } else {
                    console.log(`📭 ${currentSearchDate.toDateString()} 开始的 ${initialDaysBack} 天内没有照片`);
                }

                // 向前推进搜索日期
                currentSearchDate.setDate(currentSearchDate.getDate() - initialDaysBack);
                daysSearched += initialDaysBack;

                // 如果照片数量仍然不足，但已经搜索了很多天，可以考虑增加每次搜索的天数
                if (totalPhotos.length < minPhotos / 2 && daysSearched > 30) {
                    initialDaysBack = Math.min(initialDaysBack * 2, 30); // 最多30天一批
                    console.log(`📈 增加搜索范围到 ${initialDaysBack} 天/批`);
                }
            }

            if (totalPhotos.length >= minPhotos) {
                console.log(`✅ 智能加载完成！获得 ${totalPhotos.length} 张照片（目标 ${minPhotos} 张）`);
                console.log(`📊 搜索了 ${daysSearched} 天，平均每天 ${(totalPhotos.length / daysSearched).toFixed(2)} 张照片`);
            } else {
                console.log(`⚠️ 智能加载结束，仅获得 ${totalPhotos.length} 张照片（目标 ${minPhotos} 张）`);
                console.log(`📊 已搜索 ${daysSearched} 天，可能照片库中照片数量有限`);
                setHasMorePhotos(false);
            }

            // 设置最终的照片列表
            setPhotos(totalPhotos);

            // 按日期分组照片并更新currentDate为实际的最新照片日期
            if (totalPhotos.length > 0) {
                // 按日期分组
                groupPhotosByDate(totalPhotos);

                // 找到最新的照片日期，更新currentDate
                const latestPhoto = totalPhotos.reduce((latest, photo) =>
                    photo.date_taken > latest.date_taken ? photo : latest
                );
                const latestPhotoDate = new Date(latestPhoto.date_taken * 1000);
                setCurrentDate(latestPhotoDate);
                console.log(`📅 更新显示日期为最新照片日期: ${latestPhotoDate.toDateString()}`);

                console.log('🔍 开始分析照片相似性...');
                await analyzePhotos(totalPhotos);
            }

        } catch (error) {
            console.error('❌ 智能加载失败:', error);
            throw error;
        }
    }

    // 加载指定天数范围的照片
    async function loadPhotosForDateRange(
        startDate: Date,
        daysCount: number,
        maxPhotos?: number,
        libraryPath?: string
    ) {
        try {
            setIsLoadingMore(true);

            // 计算开始和结束时间戳
            const startOfRange = new Date(startDate);
            startOfRange.setHours(0, 0, 0, 0);

            const endOfRange = new Date(startDate);
            endOfRange.setDate(endOfRange.getDate() - daysCount + 1); // 向前推daysCount天，但包含起始日
            endOfRange.setHours(0, 0, 0, 0); // 设置为最早日期的开始

            const startTimestamp = Math.floor(endOfRange.getTime() / 1000); // 注意：这里是较早的日期
            const endTimestamp = Math.floor(startOfRange.getTime() / 1000); // 这里是较晚的日期

            console.log(`🔍 加载日期范围: ${daysCount}天`);
            console.log(
                `📅 从 ${endOfRange.toDateString()} 到 ${startOfRange.toDateString()}`
            );
            console.log(`📅 时间戳范围: ${startTimestamp} - ${endTimestamp}`);

            const pathToUse = libraryPath || selectedPath;
            console.log(`📂 使用路径: ${pathToUse}`);
            console.log(`🔢 最大照片数: ${maxPhotos || '无限制'}`);

            if (!pathToUse) {
                console.error('❌ 照片库路径为空，无法加载照片');
                return [];
            }

            // 使用智能缓存版本的日期范围查询
            const rangePhotos: Photo[] = await invoke(
                'collect_photos_with_smart_cache_by_date_range',
                {
                    libraryPath: pathToUse,
                    startDate: startTimestamp,
                    endDate: endTimestamp,
                    maxPhotos: maxPhotos || null
                }
            );

            console.log(
                `✅ collect_photos_by_date_range 调用完成，返回 ${rangePhotos.length} 张照片`
            );

            if (rangePhotos.length === 0) {
                console.log(`📭 ${daysCount}天范围内没有找到照片`);
                return [];
            }

            console.log(
                `${daysCount}天范围内找到 ${rangePhotos.length} 张照片`
            );

            // 调试：检查缩略图路径
            const withThumbnails = rangePhotos.filter(p => p.thumbnail_path);
            const withoutThumbnails = rangePhotos.filter(
                p => !p.thumbnail_path
            );
            console.log(
                `📸 ${daysCount}天范围 - 有缩略图: ${withThumbnails.length}, 无缩略图: ${withoutThumbnails.length}`
            );

            if (withoutThumbnails.length > 0) {
                console.log('⚠️ 部分照片没有缩略图，可能需要生成');
                console.log(
                    '📝 无缩略图的照片示例:',
                    withoutThumbnails.slice(0, 3).map(p => p.filename)
                );
            }

            return rangePhotos;
        } catch (error) {
            console.error(`❌ 加载 ${daysCount}天范围的照片失败:`, error);
            return [];
        } finally {
            setIsLoadingMore(false);
        }
    }

    // 分批加载一天的照片
    async function loadPhotosForDate(
        date: Date,
        maxPhotos?: number,
        libraryPath?: string
    ) {
        try {
            setIsLoadingMore(true);

            // 计算当天的开始和结束时间戳
            const startOfDay = new Date(date);
            startOfDay.setHours(0, 0, 0, 0);
            const endOfDay = new Date(date);
            endOfDay.setHours(23, 59, 59, 999);

            const startTimestamp = Math.floor(startOfDay.getTime() / 1000);
            const endTimestamp = Math.floor(endOfDay.getTime() / 1000);

            console.log(`🔍 加载日期 ${date.toDateString()} 的照片`);
            console.log(`📅 时间戳范围: ${startTimestamp} - ${endTimestamp}`);
            console.log(
                `📅 日期范围: ${startOfDay.toISOString()} - ${endOfDay.toISOString()}`
            );
            const pathToUse = libraryPath || selectedPath;
            console.log(`📂 传入的libraryPath: ${libraryPath}`);
            console.log(`📂 当前selectedPath: ${selectedPath}`);
            console.log(`📂 最终使用的路径: ${pathToUse}`);
            console.log(`🔢 最大照片数: ${maxPhotos || '无限制'}`);

            if (!pathToUse) {
                console.error('❌ 照片库路径为空，无法加载照片');
                return [];
            }

            // 调用新的API获取指定日期范围的照片（不限制数量）
            console.log('🚀 开始调用 collect_photos_with_smart_cache_by_date_range...');
            console.log('📋 调用参数:', {
                libraryPath: pathToUse,
                startDate: startTimestamp,
                endDate: endTimestamp,
                maxPhotos: maxPhotos || null
            });

            const dayPhotos: Photo[] = await invoke(
                'collect_photos_with_smart_cache_by_date_range',
                {
                    libraryPath: pathToUse, // 使用驼峰命名
                    startDate: startTimestamp,
                    endDate: endTimestamp,
                    maxPhotos: maxPhotos || null // 不传递maxPhotos或传递null表示不限制
                }
            );

            console.log(
                `✅ collect_photos_with_smart_cache_by_date_range 调用完成，返回 ${dayPhotos.length} 张照片`
            );

            if (dayPhotos.length === 0) {
                console.log(`📭 ${date.toDateString()} 没有找到照片`);
                return [];
            }

            console.log(`${date.toDateString()} 找到 ${dayPhotos.length} 张照片`);

            // 调试：检查缩略图路径
            const withThumbnails = dayPhotos.filter(p => p.thumbnail_path);
            const withoutThumbnails = dayPhotos.filter(p => !p.thumbnail_path);
            console.log(
                `📸 ${date.toDateString()} - 有缩略图: ${withThumbnails.length}, 无缩略图: ${withoutThumbnails.length}`
            );

            if (withoutThumbnails.length > 0) {
                console.log('⚠️ 部分照片没有缩略图，可能需要生成');
                console.log(
                    '📝 无缩略图的照片示例:',
                    withoutThumbnails.slice(0, 3).map(p => p.filename)
                );
            }

            // 更新按日期分组的照片
            const dateKey = date.toDateString();
            setPhotosByDate(prev => {
                const newMap = new Map(prev);
                newMap.set(dateKey, dayPhotos);

                // 重新计算所有照片的扁平化列表，避免重复
                const allPhotos: Photo[] = [];
                const allUuids = new Set<string>();

                // 按日期排序，从最新到最旧
                const sortedDates = Array.from(newMap.keys()).sort(
                    (a, b) => new Date(b).getTime() - new Date(a).getTime()
                );

                for (const dateKey of sortedDates) {
                    const datePhotos = newMap.get(dateKey) || [];
                    for (const photo of datePhotos) {
                        if (!allUuids.has(photo.uuid)) {
                            allUuids.add(photo.uuid);
                            allPhotos.push(photo);
                        }
                    }
                }

                // 更新总的照片列表
                setPhotos(allPhotos);
                console.log(`📸 更新照片列表: ${allPhotos.length} 张照片 (新增 ${dayPhotos.length} 张)`);

                return newMap;
            });

            return dayPhotos;
        } catch (error) {
            console.error(`❌ 加载 ${date.toDateString()} 的照片失败:`, error);
            console.error(`❌ 错误详情:`, {
                date: date.toDateString(),
                selectedPath,
                startTimestamp: Math.floor(
                    new Date(date).setHours(0, 0, 0, 0) / 1000
                ),
                endTimestamp: Math.floor(
                    new Date(date).setHours(23, 59, 59, 999) / 1000
                ),
                error: error
            });
            return [];
        } finally {
            setIsLoadingMore(false);
        }
    }

    async function analyzePhotos(photosToAnalyze?: Photo[]) {
        const targetPhotos = photosToAnalyze || photos;
        if (targetPhotos.length === 0) {
            return;
        }

        try {
            setIsLoading(true);
            setProgress({
                phase: 'analyzing',
                current: 0,
                total: targetPhotos.length,
                message: '正在分析照片相似性...'
            });

            console.log('🔍 开始分析照片相似性，照片数量:', targetPhotos.length);
            console.log('📊 分析配置:', {
                useOptimizedAnalysis,
                optimizedConfig,
                algorithmConfig
            });

            let result: any;
            if (useOptimizedAnalysis) {
                console.log('🚀 使用优化分析器');
                result = await invoke('analyze_photos_by_timeline', {
                    photos: targetPhotos,
                    config: {
                        timeThresholdSeconds: algorithmConfig.timeThresholdSeconds,
                        similarityThreshold: algorithmConfig.similarityThreshold,
                        batchSize: optimizedConfig.batchSize,
                        maxWorkers: optimizedConfig.maxWorkers,
                        ahashThreshold: optimizedConfig.ahashThreshold,
                        phashThreshold: optimizedConfig.phashThreshold
                    }
                });
            } else {
                console.log('🔄 使用传统分析器');
                result = await invoke('analyze_photos', {
                    photos: targetPhotos,
                    timeThresholdSeconds: algorithmConfig.timeThresholdSeconds,
                    similarityThreshold: algorithmConfig.similarityThreshold
                });
            }

            console.log('🔍 完整分析结果:', result);
            console.log('🔍 相似组数量:', result.similarity_groups?.length || 0);
            console.log('🔍 所有照片数量:', result.all_photos?.length || 0);

            if (result.similarity_groups) {
                setSimilarityGroups(result.similarity_groups);
                console.log(`✅ 找到 ${result.similarity_groups.length} 个相似组`);
            } else {
                console.log('⚠️ 没有收到similarity_groups数据');
                setSimilarityGroups([]);
            }

            if (result.all_photos) {
                console.log(`📸 分析返回了 ${result.all_photos.length} 张照片`);

                // 在分批加载模式下，不要直接覆盖photos，而是更新photosByDate
                if (batchLoadingEnabled) {
                    console.log('📦 分批加载模式：更新按日期分组的照片数据');
                    // 重新按日期分组分析后的照片
                    groupPhotosByDate(result.all_photos);
                } else {
                    console.log('📦 传统模式：直接更新照片列表');
                    setPhotos(result.all_photos);
                    // 重新按日期分组
                    groupPhotosByDate(result.all_photos);
                }
            } else {
                console.log('⚠️ 没有收到all_photos数据');
            }

        } catch (error) {
            console.error('❌ 分析照片失败:', error);
            alert('分析照片时出错: ' + error);
        } finally {
            setIsLoading(false);
            setProgress(null);
        }
    }

    return (
        <div className='app'>
            <header className='app-header'>
                <div className='path-selection'>
                    {!selectedPath && !isLoading && defaultLibraryChecked && (
                        <div className='header-actions'>
                            <p className='no-default-library'>
                                未找到默认 Photos Library
                            </p>
                        </div>
                    )}

                    {isLoading && (
                        <div className='loading-status'>
                            <p>正在加载 Photos Library...</p>
                            {progress && (
                                <div className='progress-info'>
                                    <p>{progress.message}</p>
                                    <div className='progress-bar-container'>
                                        <div
                                            className='progress-bar'
                                            style={{
                                                width: `${(progress.current /
                                                    progress.total) *
                                                    100
                                                    }%`
                                            }}
                                        ></div>
                                    </div>
                                    <p>
                                        {progress.current} / {progress.total}
                                    </p>
                                </div>
                            )}
                        </div>
                    )}
                </div>
            </header>

            <div className='tab-navigation'>
                <button
                    className={activeTab === 'photos' ? 'active' : ''}
                    onClick={() => setActiveTab('photos')}
                >
                    所有照片 ({photos.length})
                </button>
                <button
                    className={activeTab === 'duplicates' ? 'active' : ''}
                    onClick={() => setActiveTab('duplicates')}
                >
                    相似照片 ({similarityGroups.length})
                </button>
                <button
                    className={
                        algorithmConfig.showAlgorithmDetails ? 'active' : ''
                    }
                    onClick={() =>
                        setAlgorithmConfig(prev => ({
                            ...prev,
                            showAlgorithmDetails: !prev.showAlgorithmDetails
                        }))
                    }
                >
                    算法参数
                </button>
            </div>

            {/* 算法参数控制面板 */}
            {algorithmConfig.showAlgorithmDetails && (
                <div className='algorithm-config-panel'>
                    <h3>算法参数配置</h3>

                    <div className='config-section'>
                        <h4>时间阈值</h4>
                        <div className='config-item'>
                            <label>
                                时间阈值 (秒):
                                <input
                                    type='number'
                                    min='60'
                                    max='3600'
                                    value={algorithmConfig.timeThresholdSeconds}
                                    onChange={e =>
                                        setAlgorithmConfig(prev => ({
                                            ...prev,
                                            timeThresholdSeconds: Number(e.target.value)
                                        }))
                                    }
                                />
                                <span className='config-description'>
                                    秒 (当前: {Math.floor(algorithmConfig.timeThresholdSeconds / 60)} 分钟)
                                </span>
                            </label>
                        </div>
                    </div>

                    <div className='config-section'>
                        <h4>相似度阈值</h4>
                        <div className='config-item'>
                            <label>
                                相似度阈值:
                                <input
                                    type='range'
                                    min='0.5'
                                    max='0.99'
                                    step='0.01'
                                    value={algorithmConfig.similarityThreshold}
                                    onChange={e =>
                                        setAlgorithmConfig(prev => ({
                                            ...prev,
                                            similarityThreshold: Number(e.target.value)
                                        }))
                                    }
                                />
                                <span className='config-description'>
                                    {(algorithmConfig.similarityThreshold * 100).toFixed(0)}%
                                </span>
                            </label>
                        </div>
                    </div>

                    <div className='config-section'>
                        <h4>加载模式</h4>
                        <div className='config-item'>
                            <label className='checkbox-label'>
                                <input
                                    type='checkbox'
                                    checked={batchLoadingEnabled}
                                    onChange={e =>
                                        setBatchLoadingEnabled(e.target.checked)
                                    }
                                />
                                启用分批加载模式 (推荐)
                                <span className='algorithm-description'>
                                    (按天分批加载，支持无限滚动，智能获取足够照片)
                                </span>
                            </label>
                        </div>

                        {batchLoadingEnabled && (
                            <div className='config-item'>
                                <label>
                                    最少照片数量:
                                    <input
                                        type='number'
                                        min='20'
                                        max='200'
                                        value={minPhotosRequired}
                                        onChange={e =>
                                            setMinPhotosRequired(Number(e.target.value))
                                        }
                                    />
                                    <span className='config-description'>
                                        张
                                    </span>
                                    <span className='config-help'>
                                        (自动加载直到达到此数量)
                                    </span>
                                </label>
                            </div>
                        )}
                    </div>

                    <div className='config-section'>
                        <h4>优化分析器</h4>
                        <div className='config-item'>
                            <label className='checkbox-label'>
                                <input
                                    type='checkbox'
                                    checked={useOptimizedAnalysis}
                                    onChange={e =>
                                        setUseOptimizedAnalysis(e.target.checked)
                                    }
                                />
                                启用优化分析器 (推荐)
                                <span className='algorithm-description'>
                                    (使用时间线分组和多阶段hash算法，性能更好)
                                </span>
                            </label>
                        </div>

                        {useOptimizedAnalysis && (
                            <>
                                <div className='config-item'>
                                    <label>
                                        批处理大小:
                                        <input
                                            type='number'
                                            min='10'
                                            max='100'
                                            value={optimizedConfig.batchSize}
                                            onChange={e =>
                                                setOptimizedConfig(prev => ({
                                                    ...prev,
                                                    batchSize: Number(e.target.value)
                                                }))
                                            }
                                        />
                                        <span className='config-description'>
                                            张/批
                                        </span>
                                    </label>
                                </div>

                                <div className='config-item'>
                                    <label>
                                        AHash阈值:
                                        <input
                                            type='range'
                                            min='0.5'
                                            max='0.9'
                                            step='0.05'
                                            value={optimizedConfig.ahashThreshold}
                                            onChange={e =>
                                                setOptimizedConfig(prev => ({
                                                    ...prev,
                                                    ahashThreshold: Number(e.target.value)
                                                }))
                                            }
                                        />
                                        <span className='config-description'>
                                            {(optimizedConfig.ahashThreshold * 100).toFixed(0)}%
                                        </span>
                                        <span className='config-help'>
                                            (第一阶段筛选)
                                        </span>
                                    </label>
                                </div>

                                <div className='config-item'>
                                    <label>
                                        PHash阈值:
                                        <input
                                            type='range'
                                            min='0.5'
                                            max='0.9'
                                            step='0.05'
                                            value={optimizedConfig.phashThreshold}
                                            onChange={e =>
                                                setOptimizedConfig(prev => ({
                                                    ...prev,
                                                    phashThreshold: Number(e.target.value)
                                                }))
                                            }
                                        />
                                        <span className='config-description'>
                                            {(optimizedConfig.phashThreshold * 100).toFixed(0)}%
                                        </span>
                                        <span className='config-help'>
                                            (第二阶段验证)
                                        </span>
                                    </label>
                                </div>
                            </>
                        )}
                    </div>

                    <div className='config-section'>
                        <h4>时间范围</h4>
                        <div className='config-item'>
                            <label>
                                查看最近:
                                <input
                                    type='number'
                                    min='1'
                                    max='30'
                                    value={daysBack}
                                    onChange={e => setDaysBack(Number(e.target.value))}
                                />
                                <span className='config-description'>天的照片</span>
                            </label>
                        </div>
                    </div>
                </div>
            )}

            <main className='app-main'>
                {isLoading && (
                    <div className='loading'>
                        <div className='spinner'></div>
                        <p>正在处理照片...</p>
                        {progress && (
                            <div className='progress-info'>
                                <p>{progress.message}</p>
                                <div className='progress-bar-container'>
                                    <div
                                        className='progress-bar'
                                        style={{
                                            width: `${(progress.current /
                                                progress.total) *
                                                100
                                                }%`
                                        }}
                                    ></div>
                                </div>
                                <p>
                                    {progress.current} / {progress.total}
                                </p>
                            </div>
                        )}
                    </div>
                )}

                {activeTab === 'photos' && photos.length > 0 && (
                    <div className='photo-controls'>
                        <label className='photos-per-row-control'>
                            每行显示图片数量:
                            <input
                                type='range'
                                min='6'
                                max='10'
                                value={photosPerRow}
                                onChange={e =>
                                    setPhotosPerRow(Number(e.target.value))
                                }
                                className='photos-per-row-slider'
                            />
                            <span className='photos-per-row-value'>
                                {photosPerRow}
                            </span>
                        </label>
                    </div>
                )}

                {activeTab === 'photos' && (
                    <div className='photos-container'>
                        {batchLoadingEnabled ? (
                            // 分批加载模式：按日期分组显示
                            <div className='photos-by-date'>
                                {Array.from(photosByDate.entries())
                                    .sort(
                                        ([dateA], [dateB]) =>
                                            new Date(dateB).getTime() -
                                            new Date(dateA).getTime()
                                    )
                                    .map(([dateKey, dayPhotos]) => (
                                        <div
                                            key={dateKey}
                                            className='date-group'
                                        >
                                            <h3 className='date-header'>
                                                {new Date(
                                                    dateKey
                                                ).toLocaleDateString('zh-CN', {
                                                    year: 'numeric',
                                                    month: 'long',
                                                    day: 'numeric',
                                                    weekday: 'long'
                                                })}{' '}
                                                ({dayPhotos.length} 张照片)
                                            </h3>
                                            <div
                                                className='photos-grid'
                                                style={{
                                                    gridTemplateColumns: `repeat(${photosPerRow}, 1fr)`
                                                }}
                                            >
                                                {dayPhotos.map(photo => (
                                                    <div
                                                        key={photo.uuid}
                                                        className='photo-card'
                                                    >
                                                        {photo.thumbnail_path ? (
                                                            <img
                                                                src={convertFileSrc(
                                                                    photo.thumbnail_path
                                                                )}
                                                                alt={
                                                                    photo.filename
                                                                }
                                                                className='photo-thumbnail'
                                                                onLoad={() => {
                                                                    console.log(
                                                                        `✅ 缩略图加载成功: ${photo.thumbnail_path}`
                                                                    );
                                                                }}
                                                                onError={e => {
                                                                    console.error(
                                                                        `❌ 缩略图加载失败: ${photo.thumbnail_path}`
                                                                    );
                                                                    console.error(
                                                                        `转换后的URL: ${convertFileSrc(
                                                                            photo.thumbnail_path ||
                                                                            ''
                                                                        )}`
                                                                    );
                                                                    e.currentTarget.style.display =
                                                                        'none';
                                                                }}
                                                            />
                                                        ) : (
                                                            <div className='photo-placeholder'>
                                                                <span>
                                                                    无缩略图
                                                                </span>
                                                                <small
                                                                    style={{
                                                                        display:
                                                                            'block',
                                                                        fontSize:
                                                                            '10px',
                                                                        color: '#666'
                                                                    }}
                                                                >
                                                                    {
                                                                        photo.filename
                                                                    }
                                                                </small>
                                                            </div>
                                                        )}
                                                        <div className='photo-info'>
                                                            <h3>
                                                                {photo.filename}
                                                            </h3>
                                                            <p>
                                                                尺寸:{' '}
                                                                {photo.width} ×{' '}
                                                                {photo.height}
                                                            </p>
                                                            <p>
                                                                大小:{' '}
                                                                {(
                                                                    photo.file_size /
                                                                    1024 /
                                                                    1024
                                                                ).toFixed(
                                                                    2
                                                                )}{' '}
                                                                MB
                                                            </p>
                                                            <p>
                                                                拍摄时间:{' '}
                                                                {new Date(
                                                                    photo.date_taken *
                                                                    1000
                                                                ).toLocaleString()}
                                                            </p>

                                                            {/* 算法详情切换按钮 */}
                                                            <button
                                                                className='toggle-details-btn'
                                                                onClick={() =>
                                                                    togglePhotoDetails(
                                                                        photo.uuid
                                                                    )
                                                                }
                                                            >
                                                                {expandedPhotoDetails.has(
                                                                    photo.uuid
                                                                )
                                                                    ? '隐藏'
                                                                    : '显示'}
                                                                算法详情
                                                            </button>

                                                            {/* 算法详情展开区域 */}
                                                            {expandedPhotoDetails.has(
                                                                photo.uuid
                                                            ) && (
                                                                    <div className='photo-algorithm-details'>
                                                                        <h4>
                                                                            Hash值 (优化分析器使用)
                                                                        </h4>
                                                                        {photo.ahash && (
                                                                            <p>
                                                                                <strong>
                                                                                    AHash (第一阶段筛选):
                                                                                </strong>{' '}
                                                                                {photo.ahash}
                                                                            </p>
                                                                        )}
                                                                        {photo.phash && (
                                                                            <p>
                                                                                <strong>
                                                                                    PHash (第二阶段验证):
                                                                                </strong>{' '}
                                                                                {photo.phash}
                                                                            </p>
                                                                        )}
                                                                        {!photo.ahash && !photo.phash && (
                                                                            <p style={{ color: '#888' }}>
                                                                                暂无hash值，请重新分析照片
                                                                            </p>
                                                                        )}

                                                                        {photo.similarity_scores &&
                                                                            Object.keys(
                                                                                photo.similarity_scores
                                                                            )
                                                                                .length >
                                                                            0 && (
                                                                                <div className='similarity-scores'>
                                                                                    <h4>
                                                                                        相似度分数
                                                                                    </h4>
                                                                                    {Object.entries(
                                                                                        photo.similarity_scores
                                                                                    )
                                                                                        .slice(
                                                                                            0,
                                                                                            3
                                                                                        )
                                                                                        .map(
                                                                                            ([
                                                                                                uuid,
                                                                                                score
                                                                                            ]) => (
                                                                                                <p
                                                                                                    key={
                                                                                                        uuid
                                                                                                    }
                                                                                                >
                                                                                                    <strong>
                                                                                                        与{' '}
                                                                                                        {uuid.substring(
                                                                                                            0,
                                                                                                            8
                                                                                                        )}
                                                                                                        ...:
                                                                                                    </strong>{' '}
                                                                                                    {(
                                                                                                        score *
                                                                                                        100
                                                                                                    ).toFixed(
                                                                                                        1
                                                                                                    )}

                                                                                                    %
                                                                                                </p>
                                                                                            )
                                                                                        )}
                                                                                </div>
                                                                            )}
                                                                    </div>
                                                                )}
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    ))}

                                {/* 加载更多指示器 */}
                                {isLoadingMore && (
                                    <div className='loading-more'>
                                        <div className='spinner'></div>
                                        <p>正在加载更多照片...</p>
                                    </div>
                                )}

                                {!hasMorePhotos && photos.length > 0 && (
                                    <div className='no-more-photos'>
                                        <p>已加载所有照片</p>
                                    </div>
                                )}
                            </div>
                        ) : (
                            // 传统模式：统一网格显示
                            <div
                                className='photos-grid'
                                style={{
                                    gridTemplateColumns: `repeat(${photosPerRow}, 1fr)`
                                }}
                            >
                                {photos.map(photo => (
                                    <div
                                        key={photo.uuid}
                                        className='photo-card'
                                    >
                                        {photo.thumbnail_path ? (
                                            <img
                                                src={convertFileSrc(
                                                    photo.thumbnail_path
                                                )}
                                                alt={photo.filename}
                                                className='photo-thumbnail'
                                                onLoad={() => {
                                                    console.log(
                                                        `✅ 缩略图加载成功: ${photo.thumbnail_path}`
                                                    );
                                                }}
                                                onError={e => {
                                                    console.error(
                                                        `❌ 缩略图加载失败: ${photo.thumbnail_path}`
                                                    );
                                                    console.error(
                                                        `转换后的URL: ${convertFileSrc(
                                                            photo.thumbnail_path ||
                                                            ''
                                                        )}`
                                                    );
                                                    e.currentTarget.style.display =
                                                        'none';
                                                }}
                                            />
                                        ) : (
                                            <div className='photo-placeholder'>
                                                <span>无缩略图</span>
                                                <small
                                                    style={{
                                                        display: 'block',
                                                        fontSize: '10px',
                                                        color: '#666'
                                                    }}
                                                >
                                                    {photo.filename}
                                                </small>
                                            </div>
                                        )}
                                        <div className='photo-info'>
                                            <h3>{photo.filename}</h3>
                                            <p>
                                                尺寸: {photo.width} ×{' '}
                                                {photo.height}
                                            </p>
                                            <p>
                                                大小:{' '}
                                                {(
                                                    photo.file_size /
                                                    1024 /
                                                    1024
                                                ).toFixed(2)}{' '}
                                                MB
                                            </p>
                                            <p>
                                                拍摄时间:{' '}
                                                {new Date(
                                                    photo.date_taken * 1000
                                                ).toLocaleString()}
                                            </p>

                                            {/* 算法详情切换按钮 */}
                                            <button
                                                className='toggle-details-btn'
                                                onClick={() =>
                                                    togglePhotoDetails(
                                                        photo.uuid
                                                    )
                                                }
                                            >
                                                {expandedPhotoDetails.has(
                                                    photo.uuid
                                                )
                                                    ? '隐藏'
                                                    : '显示'}
                                                算法详情
                                            </button>

                                            {/* 算法详情展开区域 */}
                                            {expandedPhotoDetails.has(
                                                photo.uuid
                                            ) && (
                                                    <div className='photo-algorithm-details'>
                                                        <h4>Hash值 (优化分析器使用)</h4>
                                                        {photo.ahash && (
                                                            <p>
                                                                <strong>
                                                                    AHash (第一阶段筛选):
                                                                </strong>{' '}
                                                                {photo.ahash}
                                                            </p>
                                                        )}
                                                        {photo.phash && (
                                                            <p>
                                                                <strong>
                                                                    PHash (第二阶段验证):
                                                                </strong>{' '}
                                                                {photo.phash}
                                                            </p>
                                                        )}
                                                        {!photo.ahash && !photo.phash && (
                                                            <p style={{ color: '#888' }}>
                                                                暂无hash值，请重新分析照片
                                                            </p>
                                                        )}

                                                        {photo.similarity_scores &&
                                                            Object.keys(
                                                                photo.similarity_scores
                                                            ).length > 0 && (
                                                                <div className='similarity-scores'>
                                                                    <h4>
                                                                        相似度分数
                                                                    </h4>
                                                                    {Object.entries(
                                                                        photo.similarity_scores
                                                                    )
                                                                        .slice(0, 3)
                                                                        .map(
                                                                            ([
                                                                                uuid,
                                                                                score
                                                                            ]) => (
                                                                                <p
                                                                                    key={
                                                                                        uuid
                                                                                    }
                                                                                >
                                                                                    <strong>
                                                                                        与{' '}
                                                                                        {uuid.substring(
                                                                                            0,
                                                                                            8
                                                                                        )}
                                                                                        ...:
                                                                                    </strong>{' '}
                                                                                    {(
                                                                                        score *
                                                                                        100
                                                                                    ).toFixed(
                                                                                        1
                                                                                    )}

                                                                                    %
                                                                                </p>
                                                                            )
                                                                        )}
                                                                </div>
                                                            )}
                                                    </div>
                                                )}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>
                )}

                {activeTab === 'duplicates' && similarityGroups.length > 0 && (
                    <div className='photo-controls'>
                        <label className='photos-per-row-control'>
                            每行显示图片数量:
                            <input
                                type='range'
                                min='4'
                                max='8'
                                value={duplicatesPerRow}
                                onChange={e =>
                                    setDuplicatesPerRow(Number(e.target.value))
                                }
                                className='photos-per-row-slider'
                            />
                            <span className='photos-per-row-value'>
                                {duplicatesPerRow}
                            </span>
                        </label>
                    </div>
                )}

                {activeTab === 'duplicates' && (
                    <div className='duplicate-groups'>
                        {/* 显示统计信息 */}
                        {similarityGroups.length > 0 && (
                            <div className='similarity-stats'>
                                <p>
                                    总共找到 {similarityGroups.length} 个相似组
                                </p>
                            </div>
                        )}

                        {/* 显示相似组 */}
                        {similarityGroups.map(group => (
                            <div
                                key={group.group_id}
                                className='duplicate-group'
                            >
                                <div className='group-header'>
                                    <h3>相似组 #{group.group_id}</h3>
                                    <span>包含 {group.photo_count} 张照片</span>
                                    <span>
                                        相似度:{' '}
                                        {(group.similarity_score * 100).toFixed(
                                            1
                                        )}
                                        %
                                    </span>
                                </div>
                                <div
                                    className='group-photos'
                                    style={{
                                        gridTemplateColumns: `repeat(${duplicatesPerRow}, 1fr)`
                                    }}
                                >
                                    {group.photos.map(photo => (
                                        <div
                                            key={photo.uuid}
                                            className='group-photo'
                                        >
                                            {photo.thumbnail_path ? (
                                                <img
                                                    src={convertFileSrc(
                                                        photo.thumbnail_path
                                                    )}
                                                    alt={photo.filename}
                                                    className='group-thumbnail'
                                                    onError={e => {
                                                        console.error(
                                                            `Failed to load group thumbnail: ${photo.thumbnail_path}`
                                                        );
                                                        e.currentTarget.style.display =
                                                            'none';
                                                    }}
                                                />
                                            ) : (
                                                <div className='group-placeholder'>
                                                    <span>无缩略图</span>
                                                </div>
                                            )}
                                            <div className='group-photo-info'>
                                                <h4>{photo.filename}</h4>
                                                <p>
                                                    尺寸: {photo.width} ×{' '}
                                                    {photo.height}
                                                </p>
                                                <p>
                                                    大小:{' '}
                                                    {(
                                                        photo.file_size /
                                                        1024 /
                                                        1024
                                                    ).toFixed(2)}{' '}
                                                    MB
                                                </p>
                                                <p>
                                                    拍摄时间:{' '}
                                                    {new Date(
                                                        photo.date_taken * 1000
                                                    ).toLocaleString()}
                                                </p>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        ))}
                    </div>
                )}

                {!selectedPath && !isLoading && (
                    <div className='welcome'>
                        <h2>欢迎使用照片缩略图管理器</h2>
                        <p>点击"选择照片库/目录"开始浏览和管理您的照片</p>
                    </div>
                )}
            </main>
        </div>
    );
}

export default App;